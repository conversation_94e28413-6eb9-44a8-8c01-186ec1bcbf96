#!/usr/bin/env python3
"""
Minimal test for face positioning engine
"""

print("Starting minimal test...")

try:
    print("Testing basic imports...")
    from typing import List, Tuple, Optional, Dict, Any
    print("✅ typing imports OK")
    
    from dataclasses import dataclass
    print("✅ dataclasses import OK")
    
    from enum import Enum
    print("✅ enum import OK")
    
    import logging
    print("✅ logging import OK")
    
    print("Testing face positioning module...")
    
    # Test the face positioning engine step by step
    from reframing.video.face_positioning import FaceLayoutType
    print("✅ FaceLayoutType import OK")
    
    from reframing.video.face_positioning import FaceRegion
    print("✅ FaceRegion import OK")
    
    from reframing.video.face_positioning import FacePositionLayout
    print("✅ FacePositionLayout import OK")
    
    from reframing.video.face_positioning import FacePositioningEngine
    print("✅ FacePositioningEngine import OK")
    
    # Test instantiation
    engine = FacePositioningEngine()
    print("✅ FacePositioningEngine instantiation OK")
    
    print("🎯 All tests passed! Face positioning engine is working correctly.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
