#!/usr/bin/env python3
"""
Test integration of face positioning with temporal video generator
"""

print("Testing integration step by step...")

try:
    print("1. Testing face positioning engine...")
    from reframing.video.face_positioning import FacePositioningEngine
    print("✅ FacePositioningEngine import OK")
    
    print("2. Testing temporal video generator imports...")
    from reframing.tracking.temporal_face_tracker import TrackingSequence, TemporalFaceTracker
    print("✅ TrackingSequence import OK")
    
    from reframing.crop.calculator import CropWindowCalculator
    print("✅ CropWindowCalculator import OK")
    
    from reframing.video.advanced_face_analyzer import AdvancedFaceAnalyzer
    print("✅ AdvancedFaceAnalyzer import OK")
    
    print("3. Testing temporal video generator...")
    from reframing.video.temporal_video_generator import TemporalVideoGenerator
    print("✅ TemporalVideoGenerator import OK")
    
    print("4. Testing instantiation...")
    generator = TemporalVideoGenerator()
    print("✅ TemporalVideoGenerator instantiation OK")
    
    print("5. Testing face positioning engine access...")
    engine = generator.face_positioning_engine
    print("✅ Face positioning engine accessible")
    
    print("🎯 All integration tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
