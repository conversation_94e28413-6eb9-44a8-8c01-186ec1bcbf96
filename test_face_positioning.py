#!/usr/bin/env python3
"""
Test script for face positioning implementation

This script tests the new face positioning logic with different face counts
to verify the layout requirements are met.
"""

import sys
import os
import logging

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from reframing.video.face_positioning import FacePositioningEngine, FaceLayoutType
from reframing.models.data_classes import FaceDetection

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def create_test_face(x, y, width, height, confidence=0.9):
    """Create a test face detection"""
    return FaceDetection(
        x=x, y=y, width=width, height=height, confidence=confidence,
        center_x=x + width/2, center_y=y + height/2
    )

def test_face_positioning():
    """Test face positioning with different face counts"""
    
    engine = FacePositioningEngine()
    
    # Test parameters
    frame_width = 1920
    frame_height = 1080
    target_width = 720
    target_height = 1280
    timestamp = 0.0
    
    print("🧪 Testing Face Positioning Engine")
    print("=" * 50)
    
    # Test 1: Single face (should be centered)
    print("\n1️⃣ Testing single face layout:")
    single_face = [create_test_face(500, 300, 200, 250)]
    layout1 = engine.calculate_face_positioning(
        single_face, frame_width, frame_height, target_width, target_height, timestamp
    )
    print(f"   Layout type: {layout1.layout_type.value}")
    print(f"   Regions: {len(layout1.regions)}")
    print(f"   Crop position: ({layout1.crop_x}, {layout1.crop_y})")
    print(f"   Confidence: {layout1.confidence:.3f}")
    assert layout1.layout_type == FaceLayoutType.SINGLE_CENTERED
    
    # Test 2: Two faces (should be vertical arrangement)
    print("\n2️⃣ Testing dual vertical layout:")
    two_faces = [
        create_test_face(300, 200, 150, 180),  # Leftmost face
        create_test_face(800, 400, 160, 190)   # Rightmost face
    ]
    layout2 = engine.calculate_face_positioning(
        two_faces, frame_width, frame_height, target_width, target_height, timestamp
    )
    print(f"   Layout type: {layout2.layout_type.value}")
    print(f"   Regions: {len(layout2.regions)}")
    print(f"   Face assignments: {layout2.face_assignments}")
    print(f"   Crop position: ({layout2.crop_x}, {layout2.crop_y})")
    print(f"   Confidence: {layout2.confidence:.3f}")
    assert layout2.layout_type == FaceLayoutType.DUAL_VERTICAL
    assert len(layout2.regions) == 2
    
    # Test 3: Three faces (should be 2+1 layout)
    print("\n3️⃣ Testing triple 2+1 layout:")
    three_faces = [
        create_test_face(200, 150, 140, 170),  # Top left
        create_test_face(600, 180, 150, 180),  # Top right
        create_test_face(400, 500, 160, 190)   # Bottom center
    ]
    layout3 = engine.calculate_face_positioning(
        three_faces, frame_width, frame_height, target_width, target_height, timestamp
    )
    print(f"   Layout type: {layout3.layout_type.value}")
    print(f"   Regions: {len(layout3.regions)}")
    print(f"   Face assignments: {layout3.face_assignments}")
    print(f"   Crop position: ({layout3.crop_x}, {layout3.crop_y})")
    print(f"   Confidence: {layout3.confidence:.3f}")
    assert layout3.layout_type == FaceLayoutType.TRIPLE_2_PLUS_1
    assert len(layout3.regions) == 3
    
    # Test 4: Four faces (should be 2x2 grid)
    print("\n4️⃣ Testing quad grid layout:")
    four_faces = [
        create_test_face(200, 150, 140, 170),  # Top left
        create_test_face(600, 180, 150, 180),  # Top right
        create_test_face(250, 500, 160, 190),  # Bottom left
        create_test_face(650, 520, 155, 185)   # Bottom right
    ]
    layout4 = engine.calculate_face_positioning(
        four_faces, frame_width, frame_height, target_width, target_height, timestamp
    )
    print(f"   Layout type: {layout4.layout_type.value}")
    print(f"   Regions: {len(layout4.regions)}")
    print(f"   Face assignments: {layout4.face_assignments}")
    print(f"   Crop position: ({layout4.crop_x}, {layout4.crop_y})")
    print(f"   Confidence: {layout4.confidence:.3f}")
    assert layout4.layout_type == FaceLayoutType.QUAD_GRID
    assert len(layout4.regions) == 4
    
    # Test 5: No faces (should be fallback)
    print("\n5️⃣ Testing no faces (fallback):")
    layout5 = engine.calculate_face_positioning(
        [], frame_width, frame_height, target_width, target_height, timestamp
    )
    print(f"   Layout type: {layout5.layout_type.value}")
    print(f"   Crop position: ({layout5.crop_x}, {layout5.crop_y})")
    print(f"   Confidence: {layout5.confidence:.3f}")
    assert layout5.layout_type == FaceLayoutType.SINGLE_CENTERED
    assert layout5.confidence == 0.0
    
    print("\n✅ All tests passed! Face positioning implementation is working correctly.")
    print("\n📋 Summary:")
    print("   ✓ Single face: Centered both horizontally and vertically")
    print("   ✓ Two faces: Vertical arrangement (leftmost top, rightmost bottom)")
    print("   ✓ Three faces: 2+1 layout (two in top half, one centered in bottom)")
    print("   ✓ Four faces: 2x2 grid layout")
    print("   ✓ No faces: Graceful fallback to center crop")

if __name__ == "__main__":
    test_face_positioning()
